import 'package:flutter/material.dart';
import 'package:swadesic/features/widgets/post_widgets/post_card.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class CommentPreviewScreen extends StatelessWidget {
  const CommentPreviewScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Create mock comment data
    Map<String, dynamic> mockData = {
      "reference": "CO202506181605222278",
      "comment_text": "Good product . <PERSON><PERSON> is a spy in disguise of a gamer. ",
      "created_date": "2025-06-18 16:05:22.665768+05:30",
      "handle": "kitten_kreeps",
      "icon": "store_icons/wallpaperflare.com_wallpaper (3).jpg",
      "name": "Kitten Kreeps",
      "is_deleted": false,
      "like_count": 0,
      "comment_count": 0,
      "repost_count": 0,
      "repost_plus_count": 0,
      "save_count": 0,
      "share_count": 0,
      "analytics_view_count": 0,
      "rating_count": 5.0,
      "level": 1,
      "comment_type": "REVIEW",
      "main_parent_id": "P1748335610278489ULRX",
      "commenter_reference": "U1719579800140",
      "user_reference": "U1719579800140",
      "store_reference": null,
      "tagged_references_json": [],
      "tagged_users_count": 0,
      "tagged_stores_count": 0,
      "tagged_products_count": 0,
      "comment_images": [],
      "content_header_text": "verified review @kitten_kreeps/digene-medicine",
      "save_status": false,
      "repost_status": false,
      "content_category": "POST",
      "like_status": false,
      "content_type": "COMMENT"
    };

    // Convert to PostDetail
    PostDetail mockComment = PostDetail.fromCommentJson(mockData);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Comment Preview',
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        backgroundColor: AppColors.appWhite,
        elevation: 1,
        iconTheme: const IconThemeData(color: AppColors.appBlack),
      ),
      backgroundColor: AppColors.appWhite,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.lightGray.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Mock Comment Data Preview',
                    style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'This shows how the comment data will appear in the PostCard widget with rating display.',
                    style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Comment Details
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.lightGray),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Comment Details:',
                    style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 8),
                  Text('Reference: ${mockComment.postOrCommentReference}'),
                  Text('Type: ${mockComment.commentType}'),
                  Text('Rating: ${mockComment.ratingCount}'),
                  Text('Creator: ${mockComment.createdBy?.handle}'),
                  Text('Entity Type: ${mockComment.createdBy?.entityType}'),
                ],
              ),
            ),
            
            const SizedBox(height: 20),
            
            // PostCard Preview
            Text(
              'PostCard Preview:',
              style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            const SizedBox(height: 12),
            
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.lightGray),
                borderRadius: BorderRadius.circular(8),
              ),
              child: PostCard(
                postDetail: mockComment,
                onTapEdit: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Edit tapped')),
                  );
                },
                onTapDelete: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Delete tapped')),
                  );
                },
                onTapHeart: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Heart tapped')),
                  );
                },
                onTapDrawer: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Drawer tapped')),
                  );
                },
                onTapProfileImage: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Profile image tapped')),
                  );
                },
                onTapShare: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Share tapped')),
                  );
                },
                onTapPost: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Post tapped')),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
