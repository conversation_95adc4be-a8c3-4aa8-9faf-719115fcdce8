class PostDetail {
  String? contentType;
  int? likeCount;
  int? commentCount;
  int? repostCount;
  int? repostPlusCount;
  int? saveCount;
  int? shareCount;
  String? createdDate;
  bool? likeStatus;
  bool? repostStatus;
  bool? saveStatus;
  String? postOrCommentReference;
  String? text;
  String? ratingCount;
  bool? isDeleted;
  int? level;
  String? commentType;
  String? mainParentId;
  String? parentCommentId;
  String? parentHandle;
  String? contentCategory;
  List<CommentHeaders>? commentHeaders;
  CreatedBy? createdBy;
  List<Images>? images;
  bool isVisited = false;
  String? contentHeaderText;
  List<ContentHeaders>? contentHeaders;
  int? analyticsViewCount;
  int? taggedUsersCount;
  int? taggedStoresCount;
  int? taggedProductsCount;
  List<ReviewedProduct>? reviewedProducts;

  PostDetail(
      {this.likeCount,
      this.commentCount,
      this.contentType,
      this.repostCount,
      this.repostPlusCount,
      this.saveCount,
      this.contentHeaderText,
      this.shareCount,
      this.createdDate,
      this.likeStatus,
      this.repostStatus,
      this.contentCategory,
      this.saveStatus,
      this.postOrCommentReference,
      this.text,
      this.isDeleted,
      this.parentHandle,
      this.ratingCount,
      this.level,
      this.commentType,
      this.mainParentId,
      this.parentCommentId,
      this.commentHeaders,
      this.createdBy,
      this.contentHeaders,
      this.images,
      this.analyticsViewCount,
      this.taggedUsersCount,
      this.taggedStoresCount,
      this.taggedProductsCount,
      this.reviewedProducts,
      });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PostDetail &&
          runtimeType == other.runtimeType &&
          postOrCommentReference == other.postOrCommentReference;

  @override
  int get hashCode => postOrCommentReference.hashCode;

  PostDetail.fromJson(Map<String, dynamic> json) {
    contentType = json['contentType'] ?? json['content_type'] ?? "POST";
    likeCount = json['likeCount'] ?? json['like_count'] ?? 0;
    commentCount = json['commentCount'] ?? json['comment_count'] ?? 0;
    repostCount = json['repostCount'] ?? json['repost_count'] ?? 0;
    repostPlusCount = json['repostPlusCount'] ?? json['repost_plus_count'] ?? 0;
    saveCount = json['saveCount'] ?? json['save_count'] ?? 0;
    shareCount = json['shareCount'] ?? json['share_count'] ?? 0;
    createdDate = json['createdDate'] ??
        json['created_date'] ??
        "2024-12-04T09:45:28.075292";
    contentHeaderText =
        json['contentHeaderText'] ?? json['content_header_text'] ?? "";
    ratingCount = json['ratingCount'] ?? json['rating_count'] ?? "0";
    parentHandle = json['parentHandle'] ?? json['parent_handle'] ?? "";
    likeStatus = json['likeStatus'] ?? json['like_status'] ?? false;
    repostStatus = json['repostStatus'] ?? json['repost_status'] ?? false;
    contentCategory =
        json['contentCategory'] ?? json['content_category'] ?? "POST";
    saveStatus = json['saveStatus'] ?? json['save_status'] ?? false;

    analyticsViewCount =
        json['analyticsViewCount'] ?? json['analytics_view_count'] ?? 0;
    taggedUsersCount =
        json['taggedUsersCount'] ?? json['tagged_users_count'] ?? 0;
    taggedStoresCount =
        json['taggedStoresCount'] ?? json['tagged_stores_count'] ?? 0;
    taggedProductsCount =
        json['taggedProductsCount'] ?? json['tagged_products_count'] ?? 0;

    ///Post or comment reference
    postOrCommentReference = json['reference'] ??
        json['postReference'] ??
        json['post_reference'] ??
        json['commentReference'] ??
        json['comment_reference'] ??                                
        "";
    text = json['commentText'] ?? json['postText'] ?? json['post_text'] ?? json['comment_text'] ?? "";
    isDeleted = json['isDeleted'] ?? json['is_deleted'] ?? false;
    level = json['level'] ?? 0;
    commentType = json['commentType'] ?? json['comment_type'] ?? "";
    mainParentId = json['mainParentId'] ?? json['main_parent_id'] ?? "";
    parentCommentId =
        json['parentCommentId'] ?? json['parent_comment_id'] ?? "";

    ///Post or comment text
    commentHeaders = (json['commentHeaders'] ?? [])
        .map<CommentHeaders>((v) => CommentHeaders.fromJson(v))
        .toList();
    if (json['contentHeaders'] != null) {
      contentHeaders = <ContentHeaders>[];
      json['contentHeaders'].forEach((v) {
        contentHeaders!.add(new ContentHeaders.fromJson(v));
      });
    }
    createdBy = json['createdBy'] != null
        ? new CreatedBy.fromJson(json['createdBy'])
        : null;

    ///region Post and comment images
    if (json['postImages'] != null) {
      images = <Images>[];
      json['postImages'].forEach((v) {
        images!.add(new Images.fromJson(v));
      });
    } else if (json['commentImages'] != null) {
      images = <Images>[];
      json['commentImages'].forEach((v) {
        images!.add(new Images.fromJson(v));
      });
    } else if (json['comment_images'] != null) {
      images = <Images>[];
      json['comment_images'].forEach((v) {
        images!.add(new Images.fromJson(v));
      });
    } else if (json['post_images'] != null) {
      images = <Images>[];
      json['post_images'].forEach((v) {
        images!.add(Images(
          mediaId: v['post_image_id']?.toString() ??
              v['media_id']?.toString() ??
              v['mediaId']?.toString(),
          mediaType: v['media_type'] ?? v['mediaType'] ?? 'IMAGE',
          mediaPath: v['post_image'] ?? v['media_path'] ?? v['mediaPath'],
          order: v['reorder'] ?? v['order'] ?? 1,
        ));
      });
    } else {
      images = <Images>[];
    }
    //endregion
  }

  // Static method to create a PostDetail from lean API response
  static PostDetail fromLeanJson(Map<String, dynamic> json) {
    // Create post images list if available
    List<Images>? images;

    // Handle post_images from lean API
    if (json['post_images'] != null) {
      images = <Images>[];
      json['post_images'].forEach((v) {
        images!.add(Images(
          mediaId: v['post_image_id']?.toString() ??
              v['media_id']?.toString() ??
              v['mediaId']?.toString(),
          mediaType: v['media_type'] ?? v['mediaType'] ?? 'IMAGE',
          mediaPath: v['post_image'] ?? v['media_path'] ?? v['mediaPath'],
          order: v['reorder'] ?? v['order'] ?? 1,
        ));
      });
    }
    // Handle postImages from other APIs
    else if (json['postImages'] != null) {
      images = <Images>[];
      json['postImages'].forEach((v) {
        images!.add(Images.fromJson(v));
      });
    }

    // Create CreatedBy object if user data is available
    CreatedBy? createdBy;
    if (json['user_reference'] != null) {
      createdBy = CreatedBy(
        userOrStoreReference: json['user_reference'],
        entityType: 'USER',
        handle: json['handle'] ?? json['user_handle'],
        name: json['name'] ?? json['user_name'],
        icon: json['icon'] ?? json['user_icon'],
        level: json['level'] ?? "1",
      );
    } else if (json['store_reference'] != null || json['reference'] != null) {
      String storeRef = json['store_reference'] ?? json['reference'];
      createdBy = CreatedBy(
        userOrStoreReference: storeRef,
        entityType: 'STORE',
        handle: json['handle'] ?? json['store_handle'],
        name: json['name'] ?? json['store_name'],
        icon: json['icon'] ?? json['store_icon'],
        level: json['level'] ?? "1",
      );
    }

    return PostDetail(
      contentType: 'POST',
      postOrCommentReference: json['post_reference'],
      text: json['post_text'],
      createdDate: json['created_date'],
      likeCount: json['like_count'] ?? 0,
      commentCount: json['comment_count'] ?? 0,
      repostCount: json['repost_count'] ?? 0,
      repostPlusCount: json['repost_plus_count'] ?? 0,
      saveCount: json['save_count'] ?? 0,
      shareCount: json['share_count'] ?? 0,
      likeStatus: json['like_status'] ?? false,
      repostStatus: json['repost_status'] ?? false,
      saveStatus: json['save_status'] ?? false,
      isDeleted: json['is_deleted'] ?? false,
      contentCategory: json['content_category'],
      contentHeaderText: json['content_header_text'],
      analyticsViewCount: json['analytics_view_count'] ?? 0,
      taggedUsersCount: json['tagged_users_count'] ?? 0,
      taggedStoresCount: json['tagged_stores_count'] ?? 0,
      taggedProductsCount: json['tagged_products_count'] ?? 0,
      createdBy: createdBy,
      images: images,
    );
  }

  // Static method to create a PostDetail from comment API response
  static PostDetail fromCommentJson(Map<String, dynamic> json) {
    // Create comment images list if available
    List<Images>? images;

    // Handle comment_images from comment API
    if (json['comment_images'] != null && json['comment_images'] is List) {
      images = <Images>[];
      json['comment_images'].forEach((v) {
        if (v is Map<String, dynamic>) {
          images!.add(Images(
            mediaId: v['comment_image_id']?.toString() ??
                v['media_id']?.toString() ??
                v['mediaId']?.toString(),
            mediaType: v['media_type'] ?? v['mediaType'] ?? 'IMAGE',
            mediaPath: v['comment_image'] ?? v['media_path'] ?? v['mediaPath'],
            order: v['reorder'] ?? v['order'] ?? 1,
          ));
        }
      });
    }

    // Create CreatedBy object from commenter data
    CreatedBy? createdBy;
    if (json['user_reference'] != null) {
      createdBy = CreatedBy(
        userOrStoreReference: json['user_reference'],
        entityType: 'USER',
        handle: json['handle'] ?? json['user_handle'],
        name: json['name'] ?? json['user_name'],
        icon: json['icon'] ?? json['user_icon'],
        level: json['level']?.toString() ?? "1",
      );
    } else if (json['store_reference'] != null) {
      createdBy = CreatedBy(
        userOrStoreReference: json['store_reference'],
        entityType: 'STORE',
        handle: json['handle'] ?? json['store_handle'],
        name: json['name'] ?? json['store_name'],
        icon: json['icon'] ?? json['store_icon'],
        level: json['level']?.toString() ?? "1",
      );
    } else if (json['commenter_reference'] != null) {
      // Determine entity type based on commenter_reference prefix
      String entityType = json['commenter_reference'].toString().startsWith('U') ? 'USER' : 'STORE';
      createdBy = CreatedBy(
        userOrStoreReference: json['commenter_reference'],
        entityType: entityType,
        handle: json['handle'] ?? json['commenter_handle'],
        name: json['name'] ?? json['commenter_name'],
        icon: json['icon'] ?? json['commenter_icon'],
        level: json['level']?.toString() ?? "1",
      );
    }

    // Handle reviewed_reference_json for review posts
    List<ReviewedProduct>? reviewedProducts;
    if (json['reviewed_reference_json'] != null && json['reviewed_reference_json'] is List) {
      reviewedProducts = <ReviewedProduct>[];
      json['reviewed_reference_json'].forEach((v) {
        if (v is Map<String, dynamic>) {
          reviewedProducts!.add(ReviewedProduct.fromJson(v));
        }
      });
    }

    // Handle content_headers from the new API response
    List<ContentHeaders>? contentHeaders;
    if (json['content_headers'] != null && json['content_headers'] is List) {
      contentHeaders = <ContentHeaders>[];
      json['content_headers'].forEach((v) {
        if (v is Map<String, dynamic>) {
          contentHeaders!.add(ContentHeaders.fromJson(v));
        }
      });
    }

    return PostDetail(
      contentType: json['content_type'] ?? 'COMMENT',
      postOrCommentReference: json['comment_reference'] ?? json['reference'], // New API uses comment_reference
      text: json['comment_text'],
      createdDate: json['created_date'],
      likeCount: json['like_count'] ?? 0,
      commentCount: json['comment_count'] ?? 0,
      repostCount: json['repost_count'] ?? 0,
      repostPlusCount: json['repost_plus_count'] ?? 0,
      saveCount: json['save_count'] ?? 0,
      shareCount: json['share_count'] ?? 0,
      likeStatus: json['like_status'] ?? false,
      repostStatus: json['repost_status'] ?? false,
      saveStatus: json['save_status'] ?? false,
      isDeleted: json['is_deleted'] ?? false,
      contentCategory: json['content_category'] ?? 'POST',
      contentHeaderText: json['content_header_text'],
      analyticsViewCount: json['analytics_view_count'] ?? 0,
      taggedUsersCount: json['tagged_users_count'] ?? 0,
      taggedStoresCount: json['tagged_stores_count'] ?? 0,
      taggedProductsCount: json['tagged_products_count'] ?? 0,
      ratingCount: json['rating_count']?.toString(),
      commentType: json['comment_type'],
      level: json['level'] ?? 1,
      mainParentId: json['main_parent_id'],
      createdBy: createdBy,
      images: images,
      reviewedProducts: reviewedProducts,
      contentHeaders: contentHeaders,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['contentType'] = contentType;
    data['likeCount'] = likeCount;
    data['commentCount'] = commentCount;
    data['repostCount'] = repostCount;
    data['repostPlusCount'] = repostPlusCount;
    data['saveCount'] = saveCount;
    data['shareCount'] = shareCount;
    data['createdDate'] = createdDate;
    data['likeStatus'] = likeStatus;
    data['contentHeaderText'] = contentHeaderText;
    data['repostStatus'] = repostStatus;
    data['saveStatus'] = saveStatus;
    data['ratingCount'] = ratingCount;
    data['reference'] = postOrCommentReference;
    data['contentCategory'] = contentCategory;
    data['getComment'] = text;
    data['parentHandle'] = parentHandle;
    data['isDeleted'] = isDeleted;
    data['level'] = level;
    data['commentType'] = commentType;
    data['mainParentId'] = mainParentId;
    data['parentCommentId'] = parentCommentId;
    data['analyticsViewCount'] = analyticsViewCount;
    data['taggedUsersCount'] = taggedUsersCount;
    data['taggedStoresCount'] = taggedStoresCount;
    data['taggedProductsCount'] = taggedProductsCount;
    if (commentHeaders != null) {
      data['commentHeaders'] = commentHeaders!.map((v) => v.toJson()).toList();
    }
    if (createdBy != null) {
      data['createdBy'] = createdBy!.toJson();
    }

    ///Post and comment images
    if (images != null && images!.isNotEmpty) {
      data['postImages'] = images!.map((v) => v.toJson()).toList();
      data['commentImages'] = images!.map((v) => v.toJson()).toList();
    } else {
      data['postImages'] = [];
      data['commentImages'] = [];
    }

    if (contentHeaders != null) {
      data['contentHeaders'] = contentHeaders!.map((v) => v.toJson()).toList();
    }

    return data;
  }
}

class CommentHeaders {
  String? handle;
  String? reference;

  CommentHeaders({this.handle, this.reference});

  CommentHeaders.fromJson(Map<String, dynamic> json) {
    handle = json['handle'];
    reference = json['reference'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['handle'] = handle;
    data['reference'] = reference;
    return data;
  }
}

class CreatedBy {
  String? userOrStoreReference;
  String? entityType;
  String? handle;
  String? name;
  String? icon;
  String? phoneNumber;
  String? pincode;
  String? city;
  bool? isDeleted;
  String? createdDate;
  String? categoryName;
  int? followersOrSupportersCount;
  int? followingOrSupportingCount;
  String? followStatus;
  String? subscriptionType;
  String? level;

  CreatedBy(
      {this.userOrStoreReference,
      this.entityType,
      this.handle,
      this.name,
      this.icon,
      this.phoneNumber,
      this.pincode,
      this.city,
      this.isDeleted,
      this.createdDate,
      this.subscriptionType,
      this.categoryName,
      this.followersOrSupportersCount,
      this.followingOrSupportingCount,
      this.followStatus,
      this.level});

  CreatedBy.fromJson(Map<String, dynamic> json) {
    userOrStoreReference = json['reference'];
    entityType = json['entityType'];
    handle = json['handle'];
    name = json['name'];
    icon = json['icon'];
    phoneNumber = json['phoneNumber'];
    pincode = json['pincode'];
    subscriptionType = json['subscriptionType'] ?? "";
    city = json['city'];
    isDeleted = json['isDeleted'];
    createdDate = json['createdDate'];
    categoryName = json['categoryName'] ?? "";
    followersOrSupportersCount = json['followersOrSupportersCount'];
    followingOrSupportingCount = json['followingOrSupportingCount'];
    followStatus = json['followStatus'];
    level = json['level'] ?? "1";
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['reference'] = userOrStoreReference;
    data['entityType'] = entityType;
    data['handle'] = handle;
    data['name'] = name;
    data['icon'] = icon;
    data['phoneNumber'] = phoneNumber;
    data['pincode'] = pincode;
    data['city'] = city;
    data['isDeleted'] = isDeleted;
    data['createdDate'] = createdDate;
    data['categoryName'] = categoryName;
    data['followersOrSupportersCount'] = followersOrSupportersCount;
    data['followingOrSupportingCount'] = followingOrSupportingCount;
    data['followStatus'] = followStatus;
    data['level'] = level;
    return data;
  }
}

class Images {
  String? mediaId;
  String? mediaType;
  String? mediaPath;
  int? order;

  Images({this.mediaId, this.mediaType, this.mediaPath, this.order});

  Images.fromJson(Map<String, dynamic> json) {
    mediaId = json['mediaId'];
    mediaType = json['mediaType'];
    mediaPath = json['mediaPath'];
    order = json['order'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mediaId'] = mediaId;
    data['mediaType'] = mediaType;
    data['mediaPath'] = mediaPath;
    data['order'] = order;
    return data;
  }
}

class ContentHeaders {
  String? handle;
  String? reference;

  ContentHeaders({this.handle, this.reference});

  ContentHeaders.fromJson(Map<String, dynamic> json) {
    handle = json['handle'];
    reference = json['reference'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['handle'] = handle;
    data['reference'] = reference;
    return data;
  }
}

class ReviewedProduct {
  String? type;
  int? order;
  String? reference;
  String? icon;
  String? name;
  String? handle;

  ReviewedProduct({
    this.type,
    this.order,
    this.reference,
    this.icon,
    this.name,
    this.handle,
  });

  ReviewedProduct.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    order = json['order'];
    reference = json['reference'];
    icon = json['icon'];
    name = json['name'];
    handle = json['handle'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    data['order'] = order;
    data['reference'] = reference;
    data['icon'] = icon;
    data['name'] = name;
    data['handle'] = handle;
    return data;
  }
}
