import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class StoreCommentService {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  StoreCommentService() {
    httpService = HttpService();
  }

  // endregion

  // region Get Store Comments
  Future<List<PostDetail>> getStoreComments({
    required String entityReference,
    required String visitorReference,
    int limit = 100,
    int offset = 0,
    List<String> commentTypes = const ['EXTERNAL_REVIEW', 'COMMENT', 'REVIEW', 'QUESTION'],
  }) async {
    try {
      // Build comment types parameter
      String commentTypesParam = commentTypes.join('%2C');
      
      // Build URL
      String url = "${AppConstants.baseUrl}/lean/get_store_comments/"
          "?limit=$limit"
          "&offset=$offset"
          "&visitor_reference=$visitorReference"
          "&entity_reference=$entityReference"
          "&comment_types=$commentTypesParam";

      // Execute Request
      Map<String, dynamic> response = await httpService.getApiCall(url);

      // Parse response
      if (response['message'] == 'success' && response['data'] != null) {
        List<PostDetail> comments = [];

        // Add mock data at index 0
        Map<String, dynamic> mockData = {
          "reference": "CO202506181605222278",
          "comment_text": "Good product . Repto is a spy in disguise of a gamer. ",
          "created_date": "2025-06-18 16:05:22.665768+05:30",
          "content_headers": [
            {
              "handle": "krishna_k",
              "reference": "U1719579800140"
            }
          ],
          "handle": "kitten_kreeps",
          "icon": "store_icons/wallpaperflare.com_wallpaper (3).jpg",
          "name": "Kitten Kreeps",
          "quote_parent_id": null,
          "comment_id": 25,
          "is_deleted": false,
          "like_count": 0,
          "comment_count": 0,
          "repost_count": 0,
          "repost_plus_count": 0,
          "save_count": 0,
          "share_count": 0,
          "analytics_view_count": 0,
          "rating_count": 5.0,
          "level": 1,
          "comment_type": "REVIEW",
          "main_parent_id": "P1748335610278489ULRX",
          "commenter_reference": "U1719579800140",
          "user_reference": "U1719579800140",
          "store_reference": null,
          "tagged_references_json": [],
          "tagged_users_count": 0,
          "tagged_stores_count": 0,
          "tagged_products_count": 0,
          "comment_images": [],
          "content_header_text": "verified review @kitten_kreeps/digene-medicine",
          "reviewed_reference_json": [
            {
              "type": "PRODUCT",
              "order": 1,
              "reference": "P1748335610278489ULRX"
            }
          ],
          "save_status": false,
          "repost_status": false,
          "content_category": "POST",
          "like_status": false,
          "content_type": "COMMENT"
        };

        // Add mock comment at index 0
        PostDetail mockComment = PostDetail.fromCommentJson(mockData);
        comments.add(mockComment);

        for (var commentData in response['data']) {
          // Convert comment data to PostDetail using the new fromCommentJson method
          PostDetail comment = PostDetail.fromCommentJson(commentData);
          comments.add(comment);
        }

        return comments;
      } else {
        // Return mock data even if API fails for testing
        List<PostDetail> comments = [];

        Map<String, dynamic> mockData = {
          "reference": "CO202506181605222278",
          "comment_text": "Good product . Repto is a spy in disguise of a gamer. ",
          "created_date": "2025-06-18 16:05:22.665768+05:30",
          "handle": "kitten_kreeps",
          "icon": "store_icons/wallpaperflare.com_wallpaper (3).jpg",
          "name": "Kitten Kreeps",
          "is_deleted": false,
          "like_count": 0,
          "comment_count": 0,
          "repost_count": 0,
          "repost_plus_count": 0,
          "save_count": 0,
          "share_count": 0,
          "analytics_view_count": 0,
          "rating_count": 5.0,
          "level": 1,
          "comment_type": "REVIEW",
          "main_parent_id": "P1748335610278489ULRX",
          "commenter_reference": "U1719579800140",
          "user_reference": "U1719579800140",
          "store_reference": null,
          "tagged_references_json": [],
          "tagged_users_count": 0,
          "tagged_stores_count": 0,
          "tagged_products_count": 0,
          "comment_images": [],
          "content_header_text": "verified review @kitten_kreeps/digene-medicine",
          "save_status": false,
          "repost_status": false,
          "content_category": "POST",
          "like_status": false,
          "content_type": "COMMENT"
        };

        PostDetail mockComment = PostDetail.fromCommentJson(mockData);
        comments.add(mockComment);

        return comments;
      }
    } catch (e) {
      // Handle error
      print('Error fetching store comments: $e');
      return [];
    }
  }

  // endregion
}
