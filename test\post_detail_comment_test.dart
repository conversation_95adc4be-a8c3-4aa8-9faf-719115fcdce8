import 'package:flutter_test/flutter_test.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';

void main() {
  group('PostDetail.fromCommentJson', () {
    test('should create PostDetail from comment JSO<PERSON> correctly', () {
      // Sample comment JSON from the API response
      final commentJson = {
        "reference": "CO202506191659154420",
        "comment_text": "Product ki comment",
        "created_date": "2025-06-19 16:59:15.073557+05:30",
        "is_deleted": false,
        "like_count": 0,
        "comment_count": 1,
        "repost_count": 0,
        "repost_plus_count": 0,
        "save_count": 0,
        "share_count": 0,
        "analytics_view_count": 0,
        "rating_count": null,
        "level": 1,
        "comment_type": "COMMENT",
        "main_parent_id": "P1749741540045342ZYDK",
        "commenter_reference": "S1721930951916",
        "user_reference": null,
        "store_reference": "S1721930951916",
        "tagged_references_json": [],
        "tagged_users_count": 0,
        "tagged_stores_count": 0,
        "tagged_products_count": 0,
        "comment_images": [],
        "content_header_text": null,
        "reviewed_reference_json": null,
        "save_status": false,
        "repost_status": false,
        "content_category": "POST",
        "like_status": false,
        "content_type": "COMMENT"
      };

      // Create PostDetail from comment JSON
      final postDetail = PostDetail.fromCommentJson(commentJson);

      // Verify the conversion
      expect(postDetail.postOrCommentReference, equals("CO202506191659154420"));
      expect(postDetail.text, equals("Product ki comment"));
      expect(postDetail.contentType, equals("COMMENT"));
      expect(postDetail.commentType, equals("COMMENT"));
      expect(postDetail.likeCount, equals(0));
      expect(postDetail.commentCount, equals(1));
      expect(postDetail.ratingCount, isNull);
      expect(postDetail.level, equals(1));
      expect(postDetail.mainParentId, equals("P1749741540045342ZYDK"));
      expect(postDetail.createdBy?.userOrStoreReference, equals("S1721930951916"));
      expect(postDetail.createdBy?.entityType, equals("STORE"));
    });

    test('should handle comment with rating correctly', () {
      // Sample comment JSON with rating
      final commentWithRatingJson = {
        "reference": "CO202506191658520052",
        "comment_text": "Post ki comment",
        "created_date": "2025-06-19 16:58:52.505872+05:30",
        "is_deleted": false,
        "like_count": 0,
        "comment_count": 1,
        "rating_count": 4.5,
        "level": 1,
        "comment_type": "EXTERNAL_REVIEW",
        "main_parent_id": "PO202506172127562444",
        "commenter_reference": "S1721930951916",
        "store_reference": "S1721930951916",
        "comment_images": [],
        "content_type": "COMMENT"
      };

      // Create PostDetail from comment JSON
      final postDetail = PostDetail.fromCommentJson(commentWithRatingJson);

      // Verify the rating is correctly parsed
      expect(postDetail.ratingCount, equals("4.5"));
      expect(postDetail.commentType, equals("EXTERNAL_REVIEW"));
    });

    test('should handle comment images correctly', () {
      // Sample comment JSON with images
      final commentWithImagesJson = {
        "reference": "CO202506191658520052",
        "comment_text": "Comment with images",
        "comment_images": [
          {
            "comment_image_id": "123",
            "media_type": "IMAGE",
            "comment_image": "https://example.com/image1.jpg",
            "order": 1
          },
          {
            "comment_image_id": "124",
            "media_type": "IMAGE", 
            "comment_image": "https://example.com/image2.jpg",
            "order": 2
          }
        ],
        "commenter_reference": "U1719579800140",
        "content_type": "COMMENT"
      };

      // Create PostDetail from comment JSON
      final postDetail = PostDetail.fromCommentJson(commentWithImagesJson);

      // Verify images are correctly parsed
      expect(postDetail.images?.length, equals(2));
      expect(postDetail.images?[0].mediaId, equals("123"));
      expect(postDetail.images?[0].mediaPath, equals("https://example.com/image1.jpg"));
      expect(postDetail.images?[0].mediaType, equals("IMAGE"));
      expect(postDetail.images?[0].order, equals(1));
    });
  });
}
