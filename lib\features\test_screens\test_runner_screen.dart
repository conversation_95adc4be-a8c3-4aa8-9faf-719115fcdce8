import 'package:flutter/material.dart';
import 'package:swadesic/features/test_screens/comment_preview_screen.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class TestRunnerScreen extends StatelessWidget {
  const TestRunnerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Test Runner',
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        backgroundColor: AppColors.appWhite,
        elevation: 1,
        iconTheme: const IconThemeData(color: AppColors.appBlack),
      ),
      backgroundColor: AppColors.appWhite,
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Screens',
              style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            const SizedBox(height: 20),
            
            // Comment Preview Test
            Card(
              elevation: 2,
              child: ListTile(
                leading: const Icon(Icons.comment, color: AppColors.brandBlack),
                title: Text(
                  'Comment Preview',
                  style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                ),
                subtitle: Text(
                  'Preview how comment data appears in PostCard with rating',
                  style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
                ),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CommentPreviewScreen(),
                    ),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Instructions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.lightGray.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Instructions:',
                    style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '1. Tap "Comment Preview" to see how the mock comment data appears in the PostCard widget.',
                    style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '2. The preview shows the comment with a 5-star rating display.',
                    style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '3. All PostCard interactions (like, share, etc.) will show snackbar messages.',
                    style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
