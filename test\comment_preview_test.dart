import 'package:flutter_test/flutter_test.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';

void main() {
  group('Comment Preview Test', () {
    test('should create PostDetail from mock comment data and display preview info', () {
      // Mock data as provided
      Map<String, dynamic> mockData = {
        "reference": "CO202506181605222278",
        "comment_text": "Good product . <PERSON><PERSON> is a spy in disguise of a gamer. ",
        "created_date": "2025-06-18 16:05:22.665768+05:30",
        "content_headers": [
          {
            "handle": "krishna_k",
            "reference": "U1719579800140"
          }
        ],
        "handle": "kitten_kreeps",
        "icon": "store_icons/wallpaperflare.com_wallpaper (3).jpg",
        "name": "Kitten Kreeps",
        "quote_parent_id": null,
        "comment_id": 25,
        "is_deleted": false,
        "like_count": 0,
        "comment_count": 0,
        "repost_count": 0,
        "repost_plus_count": 0,
        "save_count": 0,
        "share_count": 0,
        "analytics_view_count": 0,
        "rating_count": 5.0,
        "level": 1,
        "comment_type": "REVIEW",
        "main_parent_id": "P1748335610278489ULRX",
        "commenter_reference": "U1719579800140",
        "user_reference": "U1719579800140",
        "store_reference": null,
        "tagged_references_json": [],
        "tagged_users_count": 0,
        "tagged_stores_count": 0,
        "tagged_products_count": 0,
        "comment_images": [],
        "content_header_text": "verified review @kitten_kreeps/digene-medicine",
        "reviewed_reference_json": [
          {
            "type": "PRODUCT",
            "order": 1,
            "reference": "P1748335610278489ULRX"
          }
        ],
        "save_status": false,
        "repost_status": false,
        "content_category": "POST",
        "like_status": false,
        "content_type": "COMMENT"
      };

      // Create PostDetail from mock data
      PostDetail mockComment = PostDetail.fromCommentJson(mockData);

      // Verify the conversion and print preview info
      print('\n=== COMMENT PREVIEW ===');
      print('Reference: ${mockComment.postOrCommentReference}');
      print('Comment Text: ${mockComment.text}');
      print('Comment Type: ${mockComment.commentType}');
      print('Rating: ${mockComment.ratingCount} stars');
      print('Creator Handle: ${mockComment.createdBy?.handle}');
      print('Creator Name: ${mockComment.createdBy?.name}');
      print('Creator Type: ${mockComment.createdBy?.entityType}');
      print('Created Date: ${mockComment.createdDate}');
      print('Like Count: ${mockComment.likeCount}');
      print('Content Header: ${mockComment.contentHeaderText}');
      print('======================\n');

      // Verify key fields
      expect(mockComment.postOrCommentReference, equals("CO202506181605222278"));
      expect(mockComment.text, equals("Good product . Repto is a spy in disguise of a gamer. "));
      expect(mockComment.commentType, equals("REVIEW"));
      expect(mockComment.ratingCount, equals("5.0"));
      expect(mockComment.createdBy?.handle, equals("kitten_kreeps"));
      expect(mockComment.createdBy?.name, equals("Kitten Kreeps"));
      expect(mockComment.createdBy?.entityType, equals("USER"));
      expect(mockComment.contentHeaderText, equals("verified review @kitten_kreeps/digene-medicine"));

      // Test rating display logic
      bool hasRating = mockComment.ratingCount != null && 
                       mockComment.ratingCount!.isNotEmpty && 
                       mockComment.ratingCount != "0" &&
                       mockComment.ratingCount != "null";
      
      expect(hasRating, isTrue);
      
      double rating = double.parse(mockComment.ratingCount!);
      expect(rating, equals(5.0));

      print('✅ Mock comment data successfully converted to PostDetail');
      print('✅ Rating display will show: $rating stars');
      print('✅ Comment will appear in PostCard with all functionality');
    });

    test('should show how the comment will appear in the UI', () {
      print('\n=== UI PREVIEW DESCRIPTION ===');
      print('The comment will appear in the PostCard widget with:');
      print('');
      print('📱 Header Section:');
      print('   - Profile icon: store_icons/wallpaperflare.com_wallpaper (3).jpg');
      print('   - Handle: @kitten_kreeps');
      print('   - Name: Kitten Kreeps');
      print('   - Date: 2025-06-18 16:05:22');
      print('');
      print('💬 Content Section:');
      print('   - Text: "Good product . Repto is a spy in disguise of a gamer."');
      print('   - Content Header: "verified review @kitten_kreeps/digene-medicine"');
      print('');
      print('⭐ Rating Section:');
      print('   - 5 golden stars (★★★★★)');
      print('   - Rating value: 5.0');
      print('');
      print('🔧 Action Buttons:');
      print('   - Like button (0 likes)');
      print('   - Comment button (0 comments)');
      print('   - Share button');
      print('   - Save button');
      print('');
      print('📊 Metadata:');
      print('   - Type: REVIEW');
      print('   - Reference: CO202506181605222278');
      print('   - Parent: P1748335610278489ULRX');
      print('===============================\n');
    });
  });
}
